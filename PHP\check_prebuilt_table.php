<?php
require_once 'config.php';

echo "<h2>Checking pc_prebuilt_configs table</h2>";

try {
    $link = get_db_connection();
    
    // Check if table exists
    $check_table = "SHOW TABLES LIKE 'pc_prebuilt_configs'";
    $result = mysqli_query($link, $check_table);
    
    if (!$result || mysqli_num_rows($result) == 0) {
        echo "<p style='color: red;'>❌ pc_prebuilt_configs table does not exist!</p>";
        
        // Create the table
        echo "<p>Creating pc_prebuilt_configs table...</p>";
        
        $create_sql = "CREATE TABLE pc_prebuilt_configs (
            id INT AUTO_INCREMENT PRIMARY KEY,
            config_name VARCHAR(255) NOT NULL,
            config_name_en VARCHAR(255) DEFAULT NULL,
            config_name_zh VARCHAR(255) DEFAULT NULL,
            tier ENUM('budget', 'mid_range', 'high_end', 'enthusiast') NOT NULL,
            primary_use ENUM('gaming', 'workstation', 'office', 'content_creation', 'server') NOT NULL,
            components JSON NOT NULL COMMENT 'JSON object containing component specifications',
            base_price DECIMAL(10,2) NOT NULL DEFAULT 0.00,
            current_price DECIMAL(10,2) NOT NULL DEFAULT 0.00,
            discount_percentage DECIMAL(5,2) DEFAULT 0.00,
            description TEXT DEFAULT NULL,
            description_en TEXT DEFAULT NULL,
            description_zh TEXT DEFAULT NULL,
            specifications_summary JSON DEFAULT NULL COMMENT 'JSON object with key specs for display',
            is_active BOOLEAN DEFAULT TRUE,
            sort_order INT DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_tier (tier),
            INDEX idx_primary_use (primary_use),
            INDEX idx_is_active (is_active),
            INDEX idx_sort_order (sort_order)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        if (mysqli_query($link, $create_sql)) {
            echo "<p style='color: green;'>✅ Successfully created pc_prebuilt_configs table</p>";
            
            // Insert some sample data
            echo "<p>Adding sample pre-built configurations...</p>";
            
            $sample_configs = [
                [
                    'config_name' => 'Budget Gaming PC',
                    'tier' => 'budget',
                    'primary_use' => 'gaming',
                    'components' => json_encode([
                        'cpu' => ['name' => 'AMD Ryzen 5 5600', 'price' => 200],
                        'gpu' => ['name' => 'RTX 4060', 'price' => 300],
                        'ram' => ['name' => '16GB DDR4-3200', 'price' => 80],
                        'storage' => ['name' => '500GB NVMe SSD', 'price' => 60],
                        'motherboard' => ['name' => 'B450M Pro', 'price' => 80],
                        'psu' => ['name' => '650W 80+ Bronze', 'price' => 70],
                        'case' => ['name' => 'Mid Tower ATX', 'price' => 50]
                    ]),
                    'base_price' => 840.00,
                    'current_price' => 799.00,
                    'discount_percentage' => 5.00,
                    'description' => 'Perfect entry-level gaming PC for 1080p gaming',
                    'specifications_summary' => json_encode([
                        'CPU' => 'AMD Ryzen 5 5600',
                        'GPU' => 'RTX 4060',
                        'RAM' => '16GB DDR4',
                        'Storage' => '500GB NVMe SSD'
                    ])
                ],
                [
                    'config_name' => 'High-End Gaming PC',
                    'tier' => 'high_end',
                    'primary_use' => 'gaming',
                    'components' => json_encode([
                        'cpu' => ['name' => 'Intel i7-13700K', 'price' => 400],
                        'gpu' => ['name' => 'RTX 4080', 'price' => 1200],
                        'ram' => ['name' => '32GB DDR5-5600', 'price' => 200],
                        'storage' => ['name' => '1TB NVMe SSD', 'price' => 120],
                        'motherboard' => ['name' => 'Z790 Gaming', 'price' => 200],
                        'psu' => ['name' => '850W 80+ Gold', 'price' => 120],
                        'case' => ['name' => 'Full Tower RGB', 'price' => 100]
                    ]),
                    'base_price' => 2340.00,
                    'current_price' => 2199.00,
                    'discount_percentage' => 6.00,
                    'description' => 'High-performance gaming PC for 4K gaming',
                    'specifications_summary' => json_encode([
                        'CPU' => 'Intel i7-13700K',
                        'GPU' => 'RTX 4080',
                        'RAM' => '32GB DDR5',
                        'Storage' => '1TB NVMe SSD'
                    ])
                ]
            ];
            
            foreach ($sample_configs as $config) {
                $insert_sql = "INSERT INTO pc_prebuilt_configs (config_name, tier, primary_use, components, base_price, current_price, discount_percentage, description, specifications_summary) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)";
                $stmt = mysqli_prepare($link, $insert_sql);
                mysqli_stmt_bind_param($stmt, "ssssdddss", 
                    $config['config_name'],
                    $config['tier'],
                    $config['primary_use'],
                    $config['components'],
                    $config['base_price'],
                    $config['current_price'],
                    $config['discount_percentage'],
                    $config['description'],
                    $config['specifications_summary']
                );
                
                if (mysqli_stmt_execute($stmt)) {
                    echo "<p style='color: green;'>✅ Added: " . $config['config_name'] . "</p>";
                } else {
                    echo "<p style='color: red;'>❌ Failed to add: " . $config['config_name'] . "</p>";
                }
                mysqli_stmt_close($stmt);
            }
            
        } else {
            echo "<p style='color: red;'>❌ Failed to create pc_prebuilt_configs table: " . mysqli_error($link) . "</p>";
        }
    } else {
        echo "<p style='color: green;'>✅ pc_prebuilt_configs table found</p>";
        
        // Get table structure
        $describe_result = mysqli_query($link, "DESCRIBE pc_prebuilt_configs");
        
        echo "<h3>Table Structure:</h3>";
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
        
        while ($row = mysqli_fetch_assoc($describe_result)) {
            echo "<tr>";
            echo "<td>" . $row['Field'] . "</td>";
            echo "<td>" . $row['Type'] . "</td>";
            echo "<td>" . $row['Null'] . "</td>";
            echo "<td>" . $row['Key'] . "</td>";
            echo "<td>" . $row['Default'] . "</td>";
            echo "<td>" . $row['Extra'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        // Count existing configs
        $count_result = mysqli_query($link, "SELECT COUNT(*) as count FROM pc_prebuilt_configs");
        $count_row = mysqli_fetch_assoc($count_result);
        echo "<p>Total configurations: " . $count_row['count'] . "</p>";
    }
    
    close_db_connection($link);
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . $e->getMessage() . "</p>";
}
?>

<p><a href="admin_pc_management.php">← Back to PC Management</a></p>
