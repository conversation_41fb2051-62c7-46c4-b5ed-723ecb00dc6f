<?php
require_once 'config.php';

echo "<h2>Testing PC Component Update</h2>";

try {
    $link = get_db_connection();
    
    // Check if pc_components table exists and its structure
    $check_table = "SHOW TABLES LIKE 'pc_components'";
    $result = mysqli_query($link, $check_table);
    
    if (!$result || mysqli_num_rows($result) == 0) {
        echo "<p style='color: red;'>❌ pc_components table does not exist!</p>";
        exit;
    }
    
    echo "<p style='color: green;'>✅ pc_components table found</p>";
    
    // Get table structure
    $describe_result = mysqli_query($link, "DESCRIBE pc_components");
    
    echo "<h3>Table Structure:</h3>";
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    
    $columns = [];
    while ($row = mysqli_fetch_assoc($describe_result)) {
        $columns[] = $row['Field'];
        echo "<tr>";
        echo "<td>" . $row['Field'] . "</td>";
        echo "<td>" . $row['Type'] . "</td>";
        echo "<td>" . $row['Null'] . "</td>";
        echo "<td>" . $row['Key'] . "</td>";
        echo "<td>" . $row['Default'] . "</td>";
        echo "<td>" . $row['Extra'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Check if description columns exist
    $required_columns = ['description', 'description_en', 'description_zh'];
    foreach ($required_columns as $col) {
        if (in_array($col, $columns)) {
            echo "<p style='color: green;'>✅ Column '$col' exists</p>";
        } else {
            echo "<p style='color: red;'>❌ Column '$col' does not exist</p>";
        }
    }
    
    // Test a simple update query
    echo "<h3>Testing Update Query:</h3>";
    
    // Get first component for testing
    $test_sql = "SELECT id, component_name, description FROM pc_components LIMIT 1";
    $test_result = mysqli_query($link, $test_sql);
    
    if ($test_result && mysqli_num_rows($test_result) > 0) {
        $test_component = mysqli_fetch_assoc($test_result);
        echo "<p>Testing with component ID: " . $test_component['id'] . " (" . $test_component['component_name'] . ")</p>";
        echo "<p>Current description: " . ($test_component['description'] ?: 'NULL') . "</p>";
        
        // Try to update description
        $new_description = "Test description updated at " . date('Y-m-d H:i:s');
        $update_sql = "UPDATE pc_components SET description = ? WHERE id = ?";
        $stmt = mysqli_prepare($link, $update_sql);
        
        if ($stmt) {
            mysqli_stmt_bind_param($stmt, "si", $new_description, $test_component['id']);
            $result = mysqli_stmt_execute($stmt);
            
            if ($result) {
                echo "<p style='color: green;'>✅ Update query executed successfully</p>";
                
                // Verify the update
                $verify_sql = "SELECT description FROM pc_components WHERE id = ?";
                $verify_stmt = mysqli_prepare($link, $verify_sql);
                mysqli_stmt_bind_param($verify_stmt, "i", $test_component['id']);
                mysqli_stmt_execute($verify_stmt);
                $verify_result = mysqli_stmt_get_result($verify_stmt);
                $updated_component = mysqli_fetch_assoc($verify_result);
                
                echo "<p>Updated description: " . ($updated_component['description'] ?: 'NULL') . "</p>";
                
                if ($updated_component['description'] === $new_description) {
                    echo "<p style='color: green;'>✅ Description update verified successfully</p>";
                } else {
                    echo "<p style='color: red;'>❌ Description update failed - values don't match</p>";
                }
                
                mysqli_stmt_close($verify_stmt);
            } else {
                echo "<p style='color: red;'>❌ Update query failed: " . mysqli_error($link) . "</p>";
            }
            
            mysqli_stmt_close($stmt);
        } else {
            echo "<p style='color: red;'>❌ Failed to prepare update statement: " . mysqli_error($link) . "</p>";
        }
    } else {
        echo "<p style='color: red;'>❌ No components found for testing</p>";
    }
    
    close_db_connection($link);
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . $e->getMessage() . "</p>";
}
?>
