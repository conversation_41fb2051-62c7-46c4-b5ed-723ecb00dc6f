// Form handling JavaScript for KelvinKMS.com
document.addEventListener('DOMContentLoaded', function() {
    let smsVerified = false;

    // Custom Birth Date Picker Logic
    const regBirthYearSelect = document.getElementById('regBirthYear');
    const regBirthMonthSelect = document.getElementById('regBirthMonth');
    const regBirthDaySelect = document.getElementById('regBirthDay');
    const regBirthdayInput = document.getElementById('regBirthday');

    if (regBirthYearSelect && regBirthMonthSelect && regBirthDaySelect && regBirthdayInput) {
        const today = new Date();
        const currentYear = today.getFullYear();
        const minYear = currentYear - 100;
        const maxYear = currentYear - 13;

        // Populate years
        regBirthYearSelect.innerHTML = `<option value="">${i18n.year}</option>`;
        for (let y = maxYear; y >= minYear; y--) {
            const opt = document.createElement('option');
            opt.value = y;
            opt.text = y;
            regBirthYearSelect.appendChild(opt);
        }

        // Populate months
        regBirthMonthSelect.innerHTML = `<option value="">${i18n.month}</option>`;
        for (let m = 1; m <= 12; m++) {
            const opt = document.createElement('option');
            opt.value = m;
            opt.text = m.toString().padStart(2, '0');
            regBirthMonthSelect.appendChild(opt);
        }
        
        regBirthDaySelect.innerHTML = `<option value="">${i18n.day}</option>`;

        function populateDays() {
            const year = parseInt(regBirthYearSelect.value, 10);
            const month = parseInt(regBirthMonthSelect.value, 10);
            const day = parseInt(regBirthDaySelect.value, 10);
            regBirthDaySelect.innerHTML = `<option value="">${i18n.day}</option>`;
            if (!year || !month) return;

            const daysInMonth = new Date(year, month, 0).getDate();
            for (let d = 1; d <= daysInMonth; d++) {
                const opt = document.createElement('option');
                opt.value = d;
                opt.text = d.toString().padStart(2, '0');
                regBirthDaySelect.appendChild(opt);
            }
            if (day <= daysInMonth) {
                regBirthDaySelect.value = day;
            }
        }

        function updateBirthdayInput() {
            const year = regBirthYearSelect.value;
            const month = regBirthMonthSelect.value;
            const day = regBirthDaySelect.value;

            if (year && month && day) {
                const formattedMonth = month.toString().padStart(2, '0');
                const formattedDay = day.toString().padStart(2, '0');
                regBirthdayInput.value = `${year}-${formattedMonth}-${formattedDay}`;
                if (typeof updateAgeValidation === 'function') {
                    updateAgeValidation();
                }
            } else {
                regBirthdayInput.value = '';
            }
        }

        regBirthYearSelect.addEventListener('change', () => {
            populateDays();
            updateBirthdayInput();
        });
        regBirthMonthSelect.addEventListener('change', () => {
            populateDays();
            updateBirthdayInput();
        });
        regBirthDaySelect.addEventListener('change', updateBirthdayInput);
    }

    // Validation functions
    function validateUsername(username) {
        if (username.length < 6 || username.length > 20) return false;
        if (!/^[A-Za-z0-9]/.test(username)) return false;
        if (/^[0-9]+$/.test(username)) return false;
        if (!/^[A-Za-z0-9.]+$/.test(username)) return false;
        const sensitiveWords = ['admin', 'kelvinkms', 'gm'];
        const lower = username.toLowerCase();
        return !sensitiveWords.some(word => lower.includes(word));
    }
    function validateAge(birthday) {
        const birthDate = new Date(birthday);
        const today = new Date();
        let age = today.getFullYear() - birthDate.getFullYear();
        const m = today.getMonth() - birthDate.getMonth();
        if (m < 0 || (m === 0 && today.getDate() < birthDate.getDate())) age--;
        return age >= 13;
    }
    function validateEmail(email) {
        const regex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return regex.test(email);
    }
    function validatePassword(password) {
        if (password.length < 10) return false;
        if (!/[A-Z]/.test(password)) return false;
        if (!/[a-z]/.test(password)) return false;
        if (!/[0-9]/.test(password)) return false;
        if (!/[!@#$%^&*()_+\-=\[\]{};':"\\|,\.<>\/\?]/.test(password)) return false;
        return true;
    }
    function updateUsernameRequirements(username) {
        const reqs = {
            'uname-length': username.length >= 6 && username.length <= 20,
            'uname-first': /^[A-Za-z0-9]/.test(username),
            'uname-chars': /^[A-Za-z0-9.]+$/.test(username),
            'uname-sensitive': !['admin','kelvinkms','gm'].some(w => username.toLowerCase().includes(w))
        };
        let count = 0;
        for (const [id, valid] of Object.entries(reqs)) {
            const el = document.getElementById(id);
            if (el) {
                el.className = valid ? 'requirement valid' : 'requirement invalid';
                if (valid) count++;
            }
        }
        const prog = document.getElementById('usernameProgress');
        if (prog) prog.style.width = (count / 4) * 100 + '%';
    }
    function updatePasswordRequirements(password) {
        const reqs = {
            'req-length': password.length >= 10,
            'req-uppercase': /[A-Z]/.test(password),
            'req-lowercase': /[a-z]/.test(password),
            'req-number': /[0-9]/.test(password),
            'req-special': /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)
        };
        let count = 0;
        for (const [id, valid] of Object.entries(reqs)) {
            const el = document.getElementById(id);
            if (el) {
                el.className = valid ? 'requirement valid' : 'requirement invalid';
                if (valid) count++;
            }
        }
        const prog = document.getElementById('passwordProgress');
        if (prog) prog.style.width = (count / 5) * 100 + '%';
    }

    // Element references
    const registerForm = document.getElementById('registerForm');
    const loginForm = document.getElementById('loginForm');
    const regUsername = document.getElementById('regUsername');
    const regPassword = document.getElementById('regPassword');
    const regEmail = document.getElementById('regEmail');
    const emailValidation = document.getElementById('emailValidation');
    const regEmailConfirm = document.getElementById('regEmailConfirm');
    const emailConfirmValidation = document.getElementById('emailConfirmValidation');
    // Username & Age validation elements
    const usernameValidation = document.getElementById('usernameValidation');
    const ageValidation = document.getElementById('ageValidation');
    

    const sendSmsBtn = document.getElementById('sendSmsBtn');
    const regPhone = document.getElementById('regPhone');
    const regSmsCode = document.getElementById('regSmsCode');
    const verifySmsBtn = document.getElementById('verifySmsBtn');
    const smsValidation = document.getElementById('smsValidation');
    const regGender = document.getElementById('regGender');
    const regLanguage = document.getElementById('regLanguage');

    // Gender selection handlers
    if (regGender) {
        const genderBtns = document.querySelectorAll('.gender-btn');
        genderBtns.forEach(btn => btn.addEventListener('click', () => {
            genderBtns.forEach(b => b.classList.remove('selected'));
            btn.classList.add('selected');
            regGender.value = btn.dataset.gender;
        }));
    }
    // Language selection handlers
    if (regLanguage) {
        const langBtns = document.querySelectorAll('.lang-btn');
        langBtns.forEach(btn => btn.addEventListener('click', () => {
            langBtns.forEach(b => b.classList.remove('selected'));
            btn.classList.add('selected');
            regLanguage.value = btn.dataset.lang;
        }));
    }
    // Dynamic validations
    // Password match validation
    const regPasswordConfirm = document.getElementById('regPasswordConfirm');
    const passwordMatch = document.getElementById('passwordMatch');
    function updatePasswordMatch() {
        const pwd = regPassword.value;
        const confirm = regPasswordConfirm.value;
        if (!pwd || !confirm) {
            passwordMatch.innerText = '';
            passwordMatch.className = 'password-match';
        } else if (pwd === confirm) {
            passwordMatch.innerText = i18n.js_form_passwords_match;
            passwordMatch.className = 'password-match valid';
        } else {
            passwordMatch.innerText = i18n.js_form_passwords_nomatch;
            passwordMatch.className = 'password-match invalid';
        }
    }
    if (regPassword && regPasswordConfirm && passwordMatch) {
        regPassword.addEventListener('input', updatePasswordMatch);
        regPasswordConfirm.addEventListener('input', updatePasswordMatch);
    }
    if (regUsername) regUsername.addEventListener('input', () => updateUsernameRequirements(regUsername.value));

    // Real-time username availability check
    function checkUsernameAvailability() {
        const username = regUsername.value.trim();
        if (!validateUsername(username)) {
            usernameValidation.innerText = '';
            usernameValidation.className = 'username-validation';
            return;
        }
        // Show checking status
        usernameValidation.innerText = i18n.js_form_checking_username;
        usernameValidation.className = 'username-validation checking';
        fetch('PHP/check_username.php', {
            method: 'POST',
            headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
            body: new URLSearchParams({ username })
        })
        .then(res => res.json())
        .then(data => {
            if (data.success) {
                if (data.exists) {
                    usernameValidation.innerText = i18n.js_form_username_taken;
                    usernameValidation.className = 'username-validation invalid';
                } else {
                    usernameValidation.innerText = i18n.js_form_username_available;
                    usernameValidation.className = 'username-validation valid';
                }
            } else {
                usernameValidation.innerText = data.message || i18n.js_form_error_checking_username;
                usernameValidation.className = 'username-validation invalid';
            }
        })
        .catch(() => {
            usernameValidation.innerText = i18n.js_network_error_msg;
            usernameValidation.className = 'username-validation invalid';
        });
    }
    if (regUsername && usernameValidation) {
        regUsername.addEventListener('input', checkUsernameAvailability);
        regUsername.addEventListener('blur', checkUsernameAvailability);
    }
    if (regPassword) regPassword.addEventListener('input', () => updatePasswordRequirements(regPassword.value));
    function updateEmailValidation() {
        const email = regEmail.value.trim();
        if (!email) {
            emailValidation.innerText = '';
            emailValidation.className = 'email-validation';
        } else if (!validateEmail(email)) {
            emailValidation.innerText = i18n.js_form_invalid_email_format;
            emailValidation.className = 'email-validation invalid';
        } else {
            emailValidation.innerText = i18n.js_form_valid_email_format;
            emailValidation.className = 'email-validation valid';
        }
    }
    function updateEmailConfirmValidation() {
        const email = regEmail.value.trim();
        const confirm = regEmailConfirm.value.trim();
        if (!confirm) {
            emailConfirmValidation.innerText = '';
            emailConfirmValidation.className = 'email-validation';
        } else if (!validateEmail(email)) {
            emailConfirmValidation.innerText = i18n.js_form_invalid_email_format;
            emailConfirmValidation.className = 'email-validation invalid';
        } else if (email !== confirm) {
            emailConfirmValidation.innerText = i18n.js_form_email_mismatch;
            emailConfirmValidation.className = 'email-validation invalid';
        } else {
            emailConfirmValidation.innerText = i18n.js_form_email_match;
            emailConfirmValidation.className = 'email-validation valid';
        }
    }
    if (regEmail && regEmailConfirm && emailConfirmValidation && emailValidation) {
        regEmail.addEventListener('input', updateEmailValidation);
        regEmail.addEventListener('input', updateEmailConfirmValidation);
        regEmailConfirm.addEventListener('input', updateEmailConfirmValidation);
    }

    // Real-time age validation
    function updateAgeValidation() {
        const birthday = regBirthdayInput.value;
        if (!birthday) {
            ageValidation.innerText = '';
            ageValidation.className = 'age-validation';
        } else if (!validateAge(birthday)) {
            ageValidation.innerText = i18n.js_form_age_restriction;
            ageValidation.className = 'age-validation invalid';
        } else {
            ageValidation.innerText = i18n.js_form_valid_birthday;
            ageValidation.className = 'age-validation valid';
        }
    }
    if (regBirthYearSelect && regBirthMonthSelect && regBirthDaySelect && ageValidation) {
        // Trigger validation on date picker changes
        regBirthYearSelect.addEventListener('change', updateAgeValidation);
        regBirthMonthSelect.addEventListener('change', updateAgeValidation);
        regBirthDaySelect.addEventListener('change', updateAgeValidation);
    }

    // Phone validation functions
    function validateUSPhoneNumber(phone) {
        // Remove all non-digit characters
        const digits = phone.replace(/\D/g, '');

        // Check if it's a valid US phone number (10 digits only, since we auto-add +1)
        if (digits.length === 10) {
            // 10 digits: valid US number format
            return digits.match(/^[2-9]\d{2}[2-9]\d{2}\d{4}$/);
        }

        return false;
    }
    
    function formatUSPhoneNumber(phone) {
        const digits = phone.replace(/\D/g, '');

        // Always add +1 prefix for 10-digit numbers
        if (digits.length === 10) {
            return `+1${digits}`;
        }

        return `+1${digits}`; // Fallback to add +1 prefix
    }
    
    // Format phone number input as user types
    function formatPhoneInput(input) {
        let value = input.value.replace(/\D/g, ''); // Remove non-digits

        // Limit to 10 digits
        if (value.length > 10) {
            value = value.substring(0, 10);
        }

        // Format as (XXX) XXX-XXXX
        let formattedValue = '';
        if (value.length > 0) {
            if (value.length <= 3) {
                formattedValue = `(${value}`;
            } else if (value.length <= 6) {
                formattedValue = `(${value.substring(0, 3)}) ${value.substring(3)}`;
            } else {
                formattedValue = `(${value.substring(0, 3)}) ${value.substring(3, 6)}-${value.substring(6)}`;
            }
        }

        input.value = formattedValue;
    }

    // Real-time phone validation
    const phoneValidation = document.getElementById('phoneValidation');
    function updatePhoneValidation() {
        const phone = regPhone.value.trim();

        if (!phone) {
            phoneValidation.innerText = '';
            phoneValidation.className = 'phone-validation';
            return;
        }

        if (!validateUSPhoneNumber(phone)) {
            phoneValidation.innerText = 'Please enter a valid 10-digit US phone number';
            phoneValidation.className = 'phone-validation invalid';
        } else {
            phoneValidation.innerText = 'Valid US phone number format';
            phoneValidation.className = 'phone-validation valid';
        }
    }
    
    // SMS handlers
    const resetSmsBtn = document.getElementById('resetSmsBtn');
    
    if (regPhone && phoneValidation) {
        regPhone.addEventListener('input', function() {
            formatPhoneInput(this);
            updatePhoneValidation();
        });
        regPhone.addEventListener('blur', updatePhoneValidation);
    }
    
    if (sendSmsBtn && regPhone && regSmsCode && verifySmsBtn && smsValidation && resetSmsBtn) {
        sendSmsBtn.addEventListener('click', () => {
            const phone = regPhone.value.trim();
            if (!phone) {
                smsValidation.innerText = 'Please enter your phone number first';
                smsValidation.className = 'sms-validation invalid';
                return;
            }
            
            if (!validateUSPhoneNumber(phone)) {
                smsValidation.innerText = 'Please enter a valid US phone number';
                smsValidation.className = 'sms-validation invalid';
                return;
            }
            
            const formattedPhone = formatUSPhoneNumber(phone);
            const formDataSms = new FormData();
            formDataSms.append('phone_number', formattedPhone);
            
            smsValidation.innerText = 'Sending verification code...';
            smsValidation.className = 'sms-validation checking';
            sendSmsBtn.disabled = true;
            
            fetch('PHP/send_sms_verification.php', {
                method: 'POST',
                credentials: 'same-origin',
                body: formDataSms
            })
            .then(response => response.json())
            .then(data => {
                sendSmsBtn.disabled = false;
                if (data.success) {
                    smsValidation.innerText = 'Verification code sent successfully!';
                    smsValidation.className = 'sms-validation valid';
                    regSmsCode.style.display = 'inline-block';
                    verifySmsBtn.style.display = 'inline-block';
                    resetSmsBtn.style.display = 'inline-block';
                    sendSmsBtn.style.display = 'none';
                    regPhone.disabled = true;
                } else {
                    smsValidation.innerText = data.message || 'Failed to send verification code';
                    smsValidation.className = 'sms-validation invalid';
                }
            })
            .catch(() => {
                sendSmsBtn.disabled = false;
                smsValidation.innerText = 'Network error. Please check your connection and try again.';
                smsValidation.className = 'sms-validation invalid';
            });
        });
        
        verifySmsBtn.addEventListener('click', () => {
            const code = regSmsCode.value.trim();
            if (!code) {
                smsValidation.innerText = 'Please enter the verification code';
                smsValidation.className = 'sms-validation invalid';
                return;
            }
            
            const formDataVerify = new FormData();
            formDataVerify.append('phone', formatUSPhoneNumber(regPhone.value.trim()));
            formDataVerify.append('verification_code', code);
            
            smsValidation.innerText = 'Verifying code...';
            smsValidation.className = 'sms-validation checking';
            verifySmsBtn.disabled = true;
            
            fetch('PHP/verify_sms_code.php', {
                method: 'POST',
                credentials: 'same-origin',
                body: formDataVerify
            })
            .then(response => response.json())
            .then(data => {
                verifySmsBtn.disabled = false;
                if (data.success) {
                    smsValidation.innerText = 'Phone number verified successfully!';
                    smsValidation.className = 'sms-validation valid';
                    smsVerified = true;
                    regSmsCode.disabled = true;
                    verifySmsBtn.disabled = true;
                    resetSmsBtn.style.display = 'none';
                } else {
                    smsValidation.innerText = data.message || 'Incorrect verification code';
                    smsValidation.className = 'sms-validation invalid';
                }
            })
            .catch(() => {
                verifySmsBtn.disabled = false;
                smsValidation.innerText = 'Network error. Please check your connection and try again.';
                smsValidation.className = 'sms-validation invalid';
            });
        });
        
        resetSmsBtn.addEventListener('click', () => {
            // Reset SMS verification state
            smsVerified = false;
            regPhone.disabled = false;
            regPhone.value = '';
            regSmsCode.value = '';
            regSmsCode.disabled = false;
            regSmsCode.style.display = 'none';
            verifySmsBtn.disabled = false;
            verifySmsBtn.style.display = 'none';
            resetSmsBtn.style.display = 'none';
            sendSmsBtn.style.display = 'inline-block';
            
            // Clear validation messages
            smsValidation.innerText = '';
            smsValidation.className = 'sms-validation';
            phoneValidation.innerText = '';
            phoneValidation.className = 'phone-validation';
            
            // Focus on phone input
            regPhone.focus();
        });
    }

    // Register form submission
    if (registerForm) {
        registerForm.addEventListener('submit', function(e) {
            e.preventDefault();
            const firstName = document.getElementById('regFirstName').value.trim();
            const lastName = document.getElementById('regLastName').value.trim();
            const nickname = document.getElementById('regNickname').value.trim();
            const username = regUsername.value;
            const gender = document.getElementById('regGender').value;
            const birthday = document.getElementById('regBirthday').value;
            const language = document.getElementById('regLanguage').value;
            const email = regEmail.value.trim();
            const emailConfirm = regEmailConfirm.value.trim();
            const phone = regPhone.value.trim();
            const password = regPassword.value;
            const passwordConfirm = document.getElementById('regPasswordConfirm').value;
            if (!firstName || !lastName) {
                showError(i18n.last_name, i18n.js_form_invalid_name);
                return;
            }
            if (!nickname) {
                showError(i18n.nickname, i18n.js_form_invalid_nickname);
                return;
            }
            if (!validateUsername(username)) {
                showError(i18n.username, i18n.js_form_invalid_username);
                return;
            }
            if (!gender) {
                showError(i18n.gender, i18n.js_form_invalid_gender);
                return;
            }
            if (!birthday) {
                showError(i18n.birthday, i18n.js_form_invalid_birthday);
                return;
            }
            if (!validateAge(birthday)) {
                showError(i18n.birthday, i18n.js_form_age_restriction);
                return;
            }
            if (!language) {
                showError(i18n.language, i18n.js_form_invalid_language);
                return;
            }
            if (!validateEmail(email)) {
                showError(i18n.email, i18n.js_form_invalid_email);
                return;
            }
            if (email !== emailConfirm) {
                showError(i18n.email_confirm, i18n.js_form_email_mismatch);
                return;
            }
            if (!phone) {
                showError(i18n.phone, i18n.js_form_invalid_phone);
                return;
            }
            if (!smsVerified) {
                showError(i18n.phone, i18n.js_form_phone_verification_required);
                return;
            }
            if (!validatePassword(password)) {
                showError(i18n.password, i18n.js_form_invalid_password);
                return;
            }
            if (password !== passwordConfirm) {
                showError(i18n.password_confirm, i18n.js_form_password_mismatch);
                return;
            }
            showLoading(i18n.js_form_creating_account, i18n.js_form_wait_creating_account);
            const formData = new FormData();
            formData.append('first_name', firstName);
            formData.append('last_name', lastName);
            formData.append('nickname', nickname);
            formData.append('username', username);
            formData.append('gender', gender);
            formData.append('birthday', birthday);
            formData.append('language', language);
            formData.append('email', email);
            formData.append('phone_number', phone);
            formData.append('password', password);
            fetch('PHP/register.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                hideModal();
                if (data.success) {
                    document.getElementById('registerModal').style.display = 'none';
                    document.getElementById('loginModal').style.display = 'block';
                    registerForm.reset();
                    showSuccess(i18n.js_form_registration_success_title, i18n.js_form_registration_success_msg);
                } else {
                    showError(i18n.js_form_registration_failed_title, data.message || i18n.js_form_registration_failed_msg);
                }
            })
            .catch(error => {
                hideModal();
                console.error('Error:', error);
                showError(i18n.js_form_registration_failed_title, i18n.js_network_error_msg);
            });
        });
    }

    // Login form submission
    if (loginForm) {
        loginForm.addEventListener('submit', function(e) {
            e.preventDefault();
            const username = document.getElementById('loginUsername').value;
            const password = document.getElementById('loginPassword').value;
            showLoading(i18n.js_form_logging_in_title, i18n.js_form_logging_in_msg);
            const formData = new FormData();
            formData.append('username', username);
            formData.append('password', password);
            fetch('PHP/login.php', {
                method: 'POST',
                body: formData
            })
            .then(response => {
                const responseClone = response.clone();
                return response.json().catch(() => {
                    return responseClone.text().then(text => {
                        return Promise.reject({ isCustomError: true, responseText: text });
                    });
                });
            })
            .then(data => {
                hideModal();
                if (data.success) {
                    if (data.is_admin) {
                        window.location.href = 'PHP/admin.php';
                    } else {
                        window.location.href = 'PHP/member.php';
                    }
                } else {
                    showError(i18n.js_form_login_failed_title, data.message || i18n.js_form_invalid_credentials);
                }
            })
            .catch(error => {
                hideModal();
                console.error('Login Error:', error);
                if (error && error.isCustomError) {
                    showError(i18n.js_form_login_failed_title, i18n.js_form_login_server_error);
                    const errorMessageElement = document.getElementById('errorMessage');
                    if (errorMessageElement) {
                        const pre = document.createElement('pre');
                        pre.style.textAlign = 'left';
                        pre.style.whiteSpace = 'pre-wrap';
                        pre.style.wordWrap = 'break-word';
                        pre.style.maxHeight = '200px';
                        pre.style.overflowY = 'auto';
                        pre.style.background = '#f5f5f5';
                        pre.style.padding = '10px';
                        pre.style.border = '1px solid #ccc';
                        pre.style.marginTop = '10px';
                        pre.textContent = error.responseText;
                        errorMessageElement.appendChild(pre);
                    }
                } else {
                    showError(i18n.js_form_login_failed_title, i18n.js_network_error_msg);
                }
            });
        });
    }

    // Modal control functionality
    const registerModal = document.getElementById('registerModal');
    const loginModal = document.getElementById('loginModal');
    const registerCloseBtn = document.getElementById('registerCloseBtn');
    const loginCloseBtn = document.getElementById('loginCloseBtn');

    // Close button event listeners
    if (registerCloseBtn) {
        registerCloseBtn.addEventListener('click', function() {
            registerModal.style.display = 'none';
            // Re-enable body scrolling
            document.body.style.overflow = 'auto';
        });
    }

    if (loginCloseBtn) {
        loginCloseBtn.addEventListener('click', function() {
            loginModal.style.display = 'none';
            // Re-enable body scrolling
            document.body.style.overflow = 'auto';
        });
    }

    // Prevent clicking outside modal from closing it
    if (registerModal) {
        registerModal.addEventListener('click', function(event) {
            // Only close if clicking the modal background, not the content
            if (event.target === registerModal) {
                // Do nothing - prevent closing by clicking outside
                event.stopPropagation();
            }
        });
    }

    if (loginModal) {
        loginModal.addEventListener('click', function(event) {
            // Only close if clicking the modal background, not the content
            if (event.target === loginModal) {
                // Do nothing - prevent closing by clicking outside
                event.stopPropagation();
            }
        });
    }

    // Prevent background scrolling when modal is open
    function disableBackgroundScroll() {
        document.body.style.overflow = 'hidden';
    }

    function enableBackgroundScroll() {
        document.body.style.overflow = 'auto';
    }

    // Handle ESC key to close modals
    document.addEventListener('keydown', function(event) {
        if (event.key === 'Escape') {
            if (registerModal && registerModal.style.display === 'block') {
                registerModal.style.display = 'none';
                enableBackgroundScroll();
            }
            if (loginModal && loginModal.style.display === 'block') {
                loginModal.style.display = 'none';
                enableBackgroundScroll();
            }
            const forgotPasswordModal = document.getElementById('forgotPasswordModal');
            if (forgotPasswordModal && forgotPasswordModal.style.display === 'block') {
                forgotPasswordModal.style.display = 'none';
                enableBackgroundScroll();
            }
        }
    });

    // Forgot Password Modal Handling
    const forgotPasswordModal = document.getElementById('forgotPasswordModal');
    const forgotPasswordForm = document.getElementById('forgotPasswordForm');
    const forgotPasswordCloseBtn = document.getElementById('forgotPasswordCloseBtn');
    const forgotPasswordMessage = document.getElementById('forgotPasswordMessage');

    // Open forgot password modal
    window.openForgotPasswordModal = function() {
        if (forgotPasswordModal) {
            forgotPasswordModal.style.display = 'block';
            disableBackgroundScroll();
            // Clear previous messages and form
            if (forgotPasswordMessage) {
                forgotPasswordMessage.textContent = '';
                forgotPasswordMessage.className = 'form-message';
            }
            if (forgotPasswordForm) {
                forgotPasswordForm.reset();
            }
        }
    };

    // Close forgot password modal
    function closeForgotPasswordModal() {
        if (forgotPasswordModal) {
            forgotPasswordModal.style.display = 'none';
            enableBackgroundScroll();
        }
    }

    // Close button event
    if (forgotPasswordCloseBtn) {
        forgotPasswordCloseBtn.addEventListener('click', closeForgotPasswordModal);
    }

    // Prevent clicking outside modal from closing it
    if (forgotPasswordModal) {
        forgotPasswordModal.addEventListener('click', function(event) {
            if (event.target === forgotPasswordModal) {
                event.stopPropagation();
            }
        });
    }

    // Handle forgot password form submission
    if (forgotPasswordForm) {
        forgotPasswordForm.addEventListener('submit', function(event) {
            event.preventDefault();

            const username = document.getElementById('forgotUsername').value.trim();
            const email = document.getElementById('forgotEmail').value.trim();
            const phone = document.getElementById('forgotPhone').value.trim();

            if (!username || !email || !phone) {
                showForgotPasswordMessage('Please fill in all fields.', 'error');
                return;
            }

            // Show loading message
            showForgotPasswordMessage('Verifying your information...', 'info');

            // Submit form data
            const formData = new FormData();
            formData.append('username', username);
            formData.append('email', email);
            formData.append('phone', phone);

            fetch('PHP/forgot_password.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showForgotPasswordMessage('Password reset email sent successfully! Please check your email.', 'success');
                    // Clear form after successful submission
                    forgotPasswordForm.reset();
                } else {
                    showForgotPasswordMessage(data.message || 'An error occurred. Please try again.', 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showForgotPasswordMessage('Network error. Please try again.', 'error');
            });
        });
    }

    // Helper function to show messages in forgot password modal
    function showForgotPasswordMessage(message, type) {
        if (forgotPasswordMessage) {
            forgotPasswordMessage.textContent = message;
            forgotPasswordMessage.className = `form-message ${type}`;
        }
    }

});
